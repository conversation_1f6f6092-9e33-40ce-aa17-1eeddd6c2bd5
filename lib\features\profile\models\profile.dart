// // class ProfileModel {
// //   late String id;
// //   late String fullName;
// //   late String email;
// //   late String companyName;

// //   ProfileModel({
// //     required this.id,
// //     required this.fullName,
// //     required this.email,
// //     required this.companyName,
// //   });

// //   ProfileModel.fromJson(Map<String, dynamic> json) {
// //     id = json["id"];
// //     fullName = json["fullName"];
// //     email = json["email"];
// //     companyName = json["companyName"];
// //   }

// //   Map<String, dynamic> toJson() => {
// //         "id": id,
// //         "fullName": fullName,
// //         "email": email,
// //         "companyName": companyName,
// //       };
// // }

// class ProfileModel {
//   late int id;
//   late String firstName;
//   late String lastName;
//   late String? email;
//   late String? nationalID;
//   late String? dialCountryCode;
//   late String? phone;
//   late String? phoneVerifiedAt;
//   late int? isCompleted;

//   ProfileModel({
//     required this.id,
//     required this.firstName,
//     required this.lastName,
//     this.email,
//     this.nationalID,
//     this.dialCountryCode,
//     this.phone,
//     this.phoneVerifiedAt,
//     this.isCompleted,
//   });

//   ProfileModel.fromJson(Map<String, dynamic> json) {
//     id = json["id"];
//     firstName = json["first_name"];
//     lastName = json["last_name"];
//     email = json["email"];
//     nationalID = json["nationalID"];
//     dialCountryCode = json["dial_country_code"];
//     phone = json["phone"];
//     phoneVerifiedAt = json["phone_verified_at"];
//     isCompleted = json["is_completed"];
//   }

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "first_name": firstName,
//         "last_name": lastName,
//         "email": email,
//         "nationalID": nationalID,
//         "dial_country_code": dialCountryCode,
//         "phone": phone,
//         "phone_verified_at": phoneVerifiedAt,
//         "is_completed": isCompleted,
//       };
// }

class ProfileModel {
  int id;
  String firstName;
  String lastName;
  String? email;
  // String? nationalID;
  // String? dialCountryCode;
  String? phone;
  // String? phoneVerifiedAt;
  // int? isCompleted;
  int? providerId;
  // int? providerRate;
  // int? isApproved;
  // String? rejectReason;
  // double? balance;

  ProviderShortModel? provider; // موديل صغير للبروفايدر

  // int? customerId;
  // int? rate;
  // int? ordersCnt;
  // int? pendingOrdersCnt;
  // int? completedOrdersCnt;
  // int? points;
  // int? pointsInSp;

  ProfileModel({
    required this.id,
    required this.firstName,
    required this.lastName,
    this.email,
    // this.nationalID,
    // this.dialCountryCode,
    this.phone,
    // this.phoneVerifiedAt,
    // this.isCompleted,
    this.providerId,
    // this.providerRate,
    // this.isApproved,
    // this.rejectReason,
    // this.balance,
    this.provider,
    // this.customerId,
    // this.rate,
    // this.ordersCnt,
    // this.pendingOrdersCnt,
    // this.completedOrdersCnt,
    // this.points,
    // this.pointsInSp,
  });

  factory ProfileModel.fromJson(Map<String, dynamic> json) => ProfileModel(
        id: json["id"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        email: json["email"],
        // nationalID: json["nationalID"],
        // dialCountryCode: json["dial_country_code"],
        phone: json["phone"],
        // phoneVerifiedAt: json["phone_verified_at"],
        // isCompleted: json["is_completed"],
        providerId: json["provider_id"],
        // providerRate: json["provider_rate"],
        // isApproved: json["is_approved"],
        // rejectReason: json["reject_reason"],
        // balance: json["balance"] is int
        // ? (json["balance"] as int).toDouble()
        // : double.tryParse(json["balance"]?.toString() ?? ''),
        provider: json["provider"] != null
            ? ProviderShortModel.fromJson(json["provider"])
            : null,
        // customerId: json["customer_id"],
        // rate: json["rate"],
        // ordersCnt: json["orders_cnt"],
        // pendingOrdersCnt: json["pending_orders_cnt"],
        // completedOrdersCnt: json["completed_orders_cnt"],
        // points: json["points"],
        // pointsInSp: json["points_in_sp"],
      );
}

class ProviderShortModel {
  int id;
  String? name;
  // String? description;
  // String? dialCountryCode;
  String? phone;
  String? email;
  dynamic gender;
  int? userId;

  ProviderShortModel({
    required this.id,
    this.name,
    // this.description,
    // this.dialCountryCode,
    this.phone,
    this.email,
    this.gender,
    this.userId,
  });

  factory ProviderShortModel.fromJson(Map<String, dynamic> json) =>
      ProviderShortModel(
        id: json["id"],
        name: json["name"],
        // description: json["description"],
        // dialCountryCode: json["dial_country_code"],
        phone: json["phone"],
        email: json["email"],
        gender: json["gender"],
        userId: json["user_id"],
      );
}
