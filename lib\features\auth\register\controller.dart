import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:image_picker/image_picker.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';

class RegisterPageController extends GetxController {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late TextEditingController number, password;

  @override
  onInit() {
    number = TextEditingController();
    password = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    number.dispose();
    password.dispose();
    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.register,
        method: RequestMethod.Post,
        // params: {"success": true},
        body: {
          "number": number.text,
          "password": password.text,
        },
      ),
    );
    if (response.success) {
      Get.toNamed(Pages.home.value);
    } else {}
  }
}
