import 'package:dio/dio.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/services/pagination/controller.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';
import 'package:renvo_app/features/my_order/controller.dart';
import 'package:renvo_app/features/my_order/models/my_orders.dart';
import 'package:renvo_app/features/my_order/widgets/delete.dart';

class OrderPendingController extends GetxController {
  // final orders=Get.find()
  // final progress_controller = Get.put(OrderProcessingController());
  Future deleteOrder(int orderId, BuildContext context) async {
    final response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.delete_order(orderId),
        method: RequestMethod.Delete,
        copyHeader: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
      ),
    );

    if (response.success) {
      // الرسالة من الباك موجودة هنا:
      final backendMessage = response.data?['message'] ?? '';
      Get.snackbar("true", backendMessage);
      pagerController.final progress_controller = Get.put(OrderProcessingController());
      // removeWhere((e) => e.id == suggestionId);
      // favorites.refresh();
      //  favorites.removeWhere((e) => e.id == suggestionId);
      
      AppDialogs.showCustomMessage(
        context: context,
        title: "delete",
        message: backendMessage,
        isSuccess: true,
      );
    }
  }

  late PaginationController pagerController;

  Future<ResponseModel> fetchData(int page, CancelToken cancel) async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.customer_orders,
        params: {"page": page, "status": "waiting"},
        cancelToken: cancel,
      ),
    );
    return response;
  }

  refreshData() {
    pagerController.refreshData();
  }
}
