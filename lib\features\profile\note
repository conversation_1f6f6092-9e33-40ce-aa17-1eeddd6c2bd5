

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:invisio_app/core/config/app_builder.dart';
import 'package:invisio_app/core/routes/routes.dart';
import 'package:invisio_app/features/profile/controller.dart';
import 'dart:ui';
import 'package:invisio_app/core/services/state_management/widgets/obs_widget.dart';

class ProfilePage extends StatelessWidget {
  // final controller = Get.put(ProfilePageController());

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ProfilePageController());
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Expanded(
          child: Column(
            children: [
              // Profile Header
              Container(
                width: double.infinity,
                height: MediaQuery.sizeOf(context).height * 0.40,
                padding: EdgeInsets.symmetric(vertical: 20),
                child: Expanded(
                  child: Column(
                    children: [
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          Expanded(
                            child: Container(
                              height: MediaQuery.sizeOf(context).height * 0.40,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(40)),
                              child: ImageFiltered(
                                imageFilter:
                                    ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                                child: Image.asset(
                                  'assets/images/logo_invisio.png',
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            top: 40,
                            child: CircleAvatar(
                              radius: 50,
                              backgroundImage:
                                  AssetImage('assets/images/logo_invisio.png'),
                              // Replace if dynamic
                            ),
                          ),
                          // Positioned(
                          //   bottom: 0,// زر التقاط الكاميرا
                          //   child: CircleAvatar(
                          //     radius: 16,
                          //     backgroundColor: Colors.white,
                          //     child: Icon(Icons.camera_alt,
                          //         size: 18, color: Colors.purple),
                          //   ),
                          // )
                          // SizedBox(height: 34),
                          Positioned(
                            bottom: 111,
                            child: Text("USER NAME",
                                style: TextStyle(fontWeight: FontWeight.bold)),
                          )
                          // Text("+999 123 456 789",
                          //     style: TextStyle(color: Colors.grey)),

                          // Text("Edit Profile",
                          //     style: TextStyle(color: Colors.grey.shade600)),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // // Loyalty & Payment
              // Padding(
              //   padding:
              //       const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10),
              //   child: Row(
              //     children: [
              //       _buildBox(Icons.loyalty, "Loyalty Points"),
              //       SizedBox(width: 10),
              //       _buildBox(Icons.payment, "Payment"),
              //     ],
              //   ),
              // ),

              // Account Section
              Expanded(
                child: ObsVariableBuilder(
                    obs: controller.profile,
                    builder: (context, profile) {
                      return ListView(
                        padding: EdgeInsets.symmetric(horizontal: 20),
                        children: [
                          _buildListTile(
                            Icons.email,
                            "${profile?.email ?? ""}",
                          ),
                          _buildListTile(
                              Icons.info, "${profile?.fullName ?? ""}"),
                          // Obx(() => SwitchListTile(
                          //       contentPadding: EdgeInsets.zero,
                          //       title: Text("App Notifications"),
                          //       value: controller.notificationsEnabled.value,
                          //       onChanged: controller.toggleNotifications,
                          //     )),
                          _buildListTile(Icons.location_on, "My Location"),
                          _buildListTile(Icons.privacy_tip, "Privacy Policy"),
                          _buildListTile(Icons.support_agent, "Contact Us"),

                          // Divider(),

                          // _buildListTile(Icons.delete_forever, "Delete my account",
                          //     color: Colors.red),
                        ],
                      );
                    }),
              ),

              ElevatedButton(
                  onPressed: () {
                    print("clicked");
                    Get.find<AppBuilder>().logout();
                  },
                  child: _buildListTile(Icons.logout, "Logout",
                      color: Colors.red)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatColumn(String value, String label) {
    return Column(
      children: [
        Text(value,
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
        Text(label, style: TextStyle(color: Colors.grey)),
      ],
    );
  }

  Widget _buildBox(IconData icon, String title) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            Icon(icon, size: 30),
            SizedBox(height: 8),
            Text(title, style: TextStyle(fontWeight: FontWeight.w500)),
          ],
        ),
      ),
    );
  }

  Widget _buildListTile(IconData icon, String title, {Color? color}) {
    return ListTile(
      leading: Icon(icon, color: color ?? Colors.black),
      title: Text(title, style: TextStyle(color: color ?? Colors.black)),
    );
  }
}
