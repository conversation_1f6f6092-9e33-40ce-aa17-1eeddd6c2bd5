import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/core/widgets/image.dart';
import 'package:renvo_app/features/home/<USER>';
import 'package:renvo_app/features/home/<USER>/service-card.dart';
import 'package:renvo_app/features/home/<USER>/story_card.dart';
import 'package:renvo_app/gen/assets.gen.dart';

class UserHomeWidget extends StatelessWidget {
  const UserHomeWidget({
    super.key,
    required this.controller,
  });

  final HomePageController controller;

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      Container(
        height: double.infinity,
        child: AppImage(
          path: Assets.images.backgroundImg.path,
          type: ImageType.Asset,
          fit: BoxFit.fill,
        ),
      ),
      Row(
        // crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Assets.icons.renvoBackground.svg(),
          Assets.icons.vector.svg()
        ],
      ),
      // SizedBox(
      //   child: _buildServiceGrid(),
      // ),
      Positioned(
          top: 50,
          left: 50,
          child: SizedBox(height: 300, width: 300, child: buildServiceGrid())),
      Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        top: MediaQuery.sizeOf(context).height * 0.30,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(30), topRight: Radius.circular(30)),
            color: StyleRepo.white,
          ),
          child: ListView(
            // mainAxisAlignment: MainAxisAlignment.start,
            // crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 20, top: 20),
                child: Text(
                  "Curated Stories",
                  style: TextStyle(fontWeight: FontWeight.w700, fontSize: 20),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                  left: 20,
                ),
                child: Text(
                  "Discover New horizons",
                  style: TextStyle(
                    color: Colors.grey,
                  ),
                ),
              ),
              Obx(
                () => SizedBox(
                  height: 250,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: controller.stories.length,
                    itemBuilder: (context, index) {
                      final story = controller.stories[index];
                      return StoryCard(story: story);
                    },
                  ),
                ),
              ),
              SizedBox(
                height: 10,
              ),
              TextButton(
                  onPressed: () {
                    Get.toNamed(Pages.Join_Provider.value);
                  },
                  child: Text("Join as a Services Providers"))
            ],
          ),
        ),
      ),
    ]);
  }
}
