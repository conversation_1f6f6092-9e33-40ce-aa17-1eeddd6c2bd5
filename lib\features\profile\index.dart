// // import 'package:get/get.dart';
// // import 'package:renvo_app/core/config/app_builder.dart';
// // import 'package:renvo_app/core/routes/routes.dart';
// // import 'package:renvo_app/core/style/repo.dart';
// // import 'package:renvo_app/core/style/style.dart';

// // import 'dart:ui';
// // import 'package:renvo_app/core/services/state_management/widgets/obs_widget.dart';

// // import 'package:flutter/material.dart';
// // import 'package:renvo_app/features/profile/controller.dart';

// // class ProfilePage extends StatelessWidget {
// //   final String username = "USER NAME";

// //   @override
// //   Widget build(BuildContext context) {
// //     final controller = Get.put(ProfilePageController());
// //     final size = MediaQuery.of(context).size;

// //     return Scaffold(
// //       backgroundColor: Colors.white,
// //       body: Column(
// //         children: [
// //           Center(
// //             child: Container(
// //               width: size.width * 0.70,
// //               height: size.height * 0.70,
// //               decoration: BoxDecoration(
// //                 color: Colors.white,
// //                 borderRadius: BorderRadius.vertical(top: Radius.circular(32)),
// //                 boxShadow: [
// //                   BoxShadow(
// //                     color: Colors.black12,
// //                     blurRadius: 8,
// //                     offset: Offset(0, -2),
// //                   )
// //                 ],
// //               ),
// //               child: ObsVariableBuilder(
// //                 obs: controller.profile,
// //                 builder: (context, profile) {
// //                   return SingleChildScrollView(
// //                     padding: const EdgeInsets.symmetric(
// //                         horizontal: 20, vertical: 16),
// //                     child: Column(
// //                       children: [
// //                         _buildListTile(Icons.email, "${profile?.email ?? ""}"),
// //                         Divider(color: Colors.grey.shade300),
// //                         _buildListTile(
// //                             Icons.person, "${profile?.lastName ?? ""}"),
// //                         Divider(color: Colors.grey.shade300),
// //                         _buildListTile(Icons.phone, "${profile?.phone ?? ""}"),
// //                         Divider(color: Colors.grey.shade300),
// //                         _buildListTile(Icons.privacy_tip, "Privacy Policy"),
// //                         Divider(color: Colors.grey.shade300),
// //                         _buildListTile(Icons.support_agent, "Contact Us"),
// //                         Divider(color: Colors.grey.shade300),
// //                         const SizedBox(height: 20),
// //                       ],
// //                     ),
// //                   );
// //                 },
// //               ),
// //             ),
// //           ),
// //           TextButton(
// //             onPressed: () {
// //               Get.find<AppBuilder>().logout();
// //             },
// //             child: _buildListTile(Icons.logout, "Logout", color: Colors.red),
// //           ),
// //         ],
// //       ),
// //     );
// //   }
// // }

// // Widget _buildListTile(IconData icon, String title, {Color? color}) {
// //   return ListTile(
// //     leading: Icon(
// //       icon,
// //       color: color ?? const Color.fromARGB(255, 16, 60, 93),
// //     ),
// //     title: Text(title,
// //         style: TextStyle(
// //           color: color ?? const Color.fromARGB(255, 16, 60, 93),
// //         )),
// //   );
// // }

// import 'package:get/get.dart';
// import 'package:flutter/material.dart';
// import 'package:renvo_app/core/config/app_builder.dart';
// import 'package:renvo_app/core/services/state_management/widgets/obs_widget.dart';
// import 'package:renvo_app/features/profile/controller.dart';

// class ProfilePage extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     final controller = Get.put(ProfilePageController());
//     final appBuilder = Get.find<AppBuilder>();
//     final size = MediaQuery.of(context).size;

//     return Scaffold(
//       backgroundColor: Colors.white,
//       body: Column(
//         children: [
//           // زر التبديل بين وضع المستخدم والبروفايدر
//           Obx(() {
//             final profile = controller.profile.value;
//             // إذا لديه بروفايدر فقط
//             if (profile?.provider != null) {
//               return Padding(
//                 padding: const EdgeInsets.only(top: 24.0),
//                 child: ElevatedButton(
//                   onPressed: appBuilder.toggleProviderMode,
//                   child: Text(
//                     appBuilder.isProviderMode.value
//                         ? " tahwel ela user  "
//                         : " tahwel ela pro ",
//                   ),
//                 ),
//               );
//             }
//             return SizedBox.shrink(); // لا شيء إذا لم يكن بروفايدر
//           }),

//           Center(
//             child: Container(
//               width: size.width * 0.70,
//               height: size.height * 0.70,
//               decoration: BoxDecoration(
//                 color: Colors.white,
//                 borderRadius: BorderRadius.vertical(top: Radius.circular(32)),
//                 boxShadow: [
//                   BoxShadow(
//                     color: Colors.black12,
//                     blurRadius: 8,
//                     offset: Offset(0, -2),
//                   )
//                 ],
//               ),
//               child: ObsVariableBuilder(
//                 obs: controller.profile,
//                 builder: (context, profile) {
//                   String displayName = "";
//                   if (profile != null) {
//                     // إذا في وضع بروفايدر، وعنده بيانات بروفايدر
//                     if (appBuilder.isProviderMode.value &&
//                         profile.provider != null) {
//                       displayName = profile.provider?.name ?? "";
//                     } else {
//                       displayName = (profile.firstName ?? "") +
//                           " " +
//                           (profile.lastName ?? "");
//                     }
//                   }

//                   return SingleChildScrollView(
//                     padding: const EdgeInsets.symmetric(
//                         horizontal: 20, vertical: 16),
//                     child: Column(
//                       children: [
//                         // اسم المستخدم أو اسم البروفايدر
//                         Text(
//                           displayName,
//                           style: const TextStyle(
//                             fontSize: 22,
//                             fontWeight: FontWeight.bold,
//                             color: Color(0xFF5B20C7),
//                           ),
//                         ),
//                         const SizedBox(height: 10),

//                         _buildListTile(Icons.email, "${profile?.email ?? ""}"),
//                         Divider(color: Colors.grey.shade300),
//                         _buildListTile(Icons.phone, "${profile?.phone ?? ""}"),
//                         Divider(color: Colors.grey.shade300),
//                         _buildListTile(Icons.privacy_tip, "Privacy Policy"),
//                         Divider(color: Colors.grey.shade300),
//                         _buildListTile(Icons.support_agent, "Contact Us"),
//                         Divider(color: Colors.grey.shade300),
//                         const SizedBox(height: 20),
//                       ],
//                     ),
//                   );
//                 },
//               ),
//             ),
//           ),

//           TextButton(
//             onPressed: () {
//               Get.find<AppBuilder>().logout();
//             },
//             child: _buildListTile(Icons.logout, "Logout", color: Colors.red),
//           ),
//         ],
//       ),
//     );
//   }
// }

// Widget _buildListTile(IconData icon, String title, {Color? color}) {
//   return ListTile(
//     leading: Icon(
//       icon,
//       color: color ?? const Color.fromARGB(255, 16, 60, 93),
//     ),
//     title: Text(
//       title,
//       style: TextStyle(
//         color: color ?? const Color.fromARGB(255, 16, 60, 93),
//       ),
//     ),
//   );
// }

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:renvo_app/core/config/app_builder.dart';
import 'package:renvo_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:renvo_app/features/profile/controller.dart';

class ProfilePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ProfilePageController());
    final appBuilder = Get.find<AppBuilder>();
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          children: [
            // زر التبديل بين وضع المستخدم والبروفايدر
            //  final profile = controller.profile.value

            // المعلومات في صندوق أنيق
            Container(
              width: size.width * 0.90,
              margin: const EdgeInsets.only(top: 24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(32)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 8,
                    offset: Offset(0, -2),
                  )
                ],
              ),
              child: ObsVariableBuilder(
                obs: controller.profile,
                builder: (context, profile) {
                  String displayName = "";
                  if (profile != null) {
                    if (appBuilder.isProviderMode.value &&
                        profile.provider != null) {
                      displayName = profile.provider?.name ?? "";
                    } else {
                      displayName = (profile.firstName ?? "") +
                          " " +
                          (profile.lastName ?? "");
                    }
                  }

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // اسم المستخدم أو اسم البروفايدر
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 16),
                        child: Text(
                          displayName,
                          style: const TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF5B20C7),
                          ),
                        ),
                      ),
                      _buildListTile(Icons.email, "${profile?.email ?? ""}"),
                      Divider(color: Colors.grey.shade300),
                      _buildListTile(Icons.phone, "${profile?.phone ?? ""}"),
                      Divider(color: Colors.grey.shade300),
                      _buildListTile(Icons.privacy_tip, "Privacy Policy"),
                      Divider(color: Colors.grey.shade300),
                      _buildListTile(Icons.support_agent, "Contact Us"),
                      Divider(color: Colors.grey.shade300),
                      const SizedBox(height: 20),
                      ObsVariableBuilder(
                          obs: controller.profile,
                          builder: (context, profile) {
                            if (profile?.provider != null) {
                              return Padding(
                                padding: const EdgeInsets.only(top: 24.0),
                                child: ElevatedButton(
                                  onPressed: appBuilder.toggleProviderMode,
                                  child: Text(
                                    appBuilder.isProviderMode.value
                                        ? "    switch user "
                                        : "  swich pro   ",
                                  ),
                                ),
                              );
                            }
                            return SizedBox.shrink();
                          }),
                    ],
                  );
                },
              ),
            ),

            // زر تسجيل الخروج
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 18.0),
              child: TextButton(
                onPressed: () {
                  Get.find<AppBuilder>().logout();
                },
                child:
                    _buildListTile(Icons.logout, "Logout", color: Colors.red),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget _buildListTile(IconData icon, String title, {Color? color}) {
  return ListTile(
    leading: Icon(
      icon,
      color: color ?? const Color.fromARGB(255, 16, 60, 93),
    ),
    title: Text(
      title,
      style: TextStyle(
        color: color ?? const Color.fromARGB(255, 16, 60, 93),
      ),
    ),
  );
}
