// import 'package:flutter/material.dart';
// import 'package:renvo_app/core/style/repo.dart';
// import 'package:renvo_app/core/widgets/svg_icon.dart';
// import 'package:renvo_app/gen/assets.gen.dart';

// class JoinProviderPage extends StatelessWidget {
//   const JoinProviderPage({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: StyleRepo.deepBlue1,
//       body: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Assets.icons.logoAddorder.svg(),

//           const SizedBox(height: 20),

//           // العنوان
//           const Text(
//             "Select Services Type",
//             style: TextStyle(
//               fontSize: 28,
//               fontWeight: FontWeight.bold,
//               color: Colors.white,
//             ),
//           ),

//           const SizedBox(height: 10),

//           // الوصف
//           const Text(
//             "Welcome to Renva ... Join us as a service provider and get all the features of the app",
//             style: TextStyle(
//               fontSize: 14,
//               color: Colors.white70,
//             ),
//             textAlign: TextAlign.center,
//           ),

//           const SizedBox(height: 30),

//           // البطاقات الأربع
//           Expanded(
//             child: ListView(
//               children: [
//                 _buildServiceCard(
//                   icon: Icons.home_filled,
//                   title: "Household Services",
//                   subtitle: "Cleaning, Ironing & Washing",
//                   color: Colors.blueAccent,
//                 ),
//                 _buildServiceCard(
//                   icon: Icons.build,
//                   title: "Professional Services",
//                   subtitle: "Electrical, Plumbing, Painting",
//                   color: Colors.lightBlue,
//                 ),
//                 _buildServiceCard(
//                   icon: Icons.person,
//                   title: "Personal Services",
//                   subtitle: "Personal Training, Tutoring",
//                   color: Colors.deepPurpleAccent,
//                 ),
//                 _buildServiceCard(
//                   icon: Icons.local_shipping,
//                   title: "Logistical Services",
//                   subtitle: "Transport, Deliveries, Packing",
//                   color: Colors.deepPurple,
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildServiceCard({
//     required IconData icon,
//     required String title,
//     required String subtitle,
//     required Color color,
//   }) {
//     return Container(
//       margin: const EdgeInsets.only(bottom: 16),
//       padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
//       decoration: BoxDecoration(
//         color: color.withOpacity(0.9),
//         borderRadius: BorderRadius.circular(20),
//       ),
//       child: Row(
//         children: [
//           Icon(icon, size: 40, color: Colors.white),
//           const SizedBox(width: 16),
//           Expanded(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(title,
//                     style: const TextStyle(
//                         color: Colors.white,
//                         fontSize: 16,
//                         fontWeight: FontWeight.bold)),
//                 const SizedBox(height: 4),
//                 Text(
//                   subtitle,
//                   style: const TextStyle(color: Colors.white70, fontSize: 12),
//                 ),
//               ],
//             ),
//           ),
//           const Icon(Icons.arrow_forward_ios, color: Colors.white),
//         ],
//       ),
//     );
//   }
// // }

// import 'dart:ui';
// import 'package:flutter/material.dart';
// import 'package:renvo_app/core/style/repo.dart';

// class JoinProviderPage extends StatelessWidget {
//   const JoinProviderPage({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final List<Map<String, dynamic>> services = [
//       {
//         "icon": Icons.home_repair_service,
//         "title": "Household Services",
//         "subtitle": "Cleaning, moving & more ...",
//       },
//       {
//         "icon": Icons.build,
//         "title": "Professional Services",
//         "subtitle": "Electrical, Plumbing, Paint...",
//       },
//       {
//         "icon": Icons.person_outline,
//         "title": "Personal Services",
//         "subtitle": "Personal Training, Tutoring ...",
//       },
//       {
//         "icon": Icons.local_shipping,
//         "title": "Logistical Services",
//         "subtitle": "Transport, Delivery, Packing...",
//       },
//     ];

//     return Scaffold(
//       backgroundColor: StyleRepo.deepBlue1,
//       body: SafeArea(
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 18),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//               const SizedBox(height: 32),
//               const Text(
//                 "Select Services Type",
//                 style: TextStyle(
//                   color: Colors.white,
//                   fontSize: 24,
//                   fontWeight: FontWeight.bold,
//                   letterSpacing: 0.5,
//                 ),
//                 textAlign: TextAlign.center,
//               ),
//               const SizedBox(height: 16),
//               const Text(
//                 "Welcome To Renva -- Join Us As A Service Provider\nAnd Get All The Features Of The App",
//                 style: TextStyle(
//                   color: Colors.white70,
//                   fontSize: 14,
//                   fontWeight: FontWeight.w400,
//                   height: 1.5,
//                 ),
//                 textAlign: TextAlign.center,
//               ),
//               const SizedBox(height: 30),

//               // Cards with blur and transparency
//               ...services.map((service) => Padding(
//                     padding: const EdgeInsets.only(bottom: 18.0),
//                     child: ClipRRect(
//                       borderRadius: BorderRadius.circular(20),
//                       child: BackdropFilter(
//                         filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
//                         child: Container(
//                           padding: const EdgeInsets.symmetric(
//                               vertical: 18, horizontal: 16),
//                           decoration: BoxDecoration(
//                             color: const Color.fromARGB(255, 207, 206, 206)
//                                 .withOpacity(0.10),
//                             borderRadius: BorderRadius.circular(20),
//                             // border: Border.all(
//                             //     color: Colors.white.withOpacity(0.23),
//                             //     width: 2),
//                           ),
//                           child: Row(
//                             children: [
//                               Container(
//                                 padding: const EdgeInsets.all(12),
//                                 decoration: BoxDecoration(
//                                   color: Colors.white.withOpacity(0.12),
//                                   borderRadius: BorderRadius.circular(14),
//                                 ),
//                                 child: Icon(
//                                   service["icon"],
//                                   color: Colors.white,
//                                   size: 32,
//                                 ),
//                               ),
//                               const SizedBox(width: 20),
//                               Expanded(
//                                 child: Column(
//                                   crossAxisAlignment: CrossAxisAlignment.start,
//                                   children: [
//                                     Text(
//                                       service["title"],
//                                       style: const TextStyle(
//                                         color: Colors.white,
//                                         fontWeight: FontWeight.w600,
//                                         fontSize: 16,
//                                       ),
//                                     ),
//                                     const SizedBox(height: 5),
//                                     Text(
//                                       service["subtitle"],
//                                       style: const TextStyle(
//                                         color: Colors.white70,
//                                         fontSize: 13,
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                               Radio(
//                                 value: false,
//                                 groupValue: true,
//                                 onChanged: (_) {},
//                                 activeColor: Colors.white,
//                               ),
//                             ],
//                           ),
//                         ),
//                       ),
//                     ),
//                   )),

//               // Next button
//               const SizedBox(height: 10),
//               SizedBox(
//                 width: double.infinity,
//                 child: ElevatedButton(
//                   style: ElevatedButton.styleFrom(
//                     backgroundColor: Colors.white,
//                     foregroundColor: const Color(0xFF5B20C7),
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(30),
//                     ),
//                     padding: const EdgeInsets.symmetric(vertical: 12),
//                   ),
//                   onPressed: () {},
//                   child: const Text(
//                     "Next",
//                     style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
//                   ),
//                 ),
//               ),
//               const SizedBox(height: 24),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:renvo_app/core/widgets/svg_icon.dart';
import 'controller.dart';
import 'package:renvo_app/features/join_provider/model/all_categories.dart'; // عدّل المسار حسب مجلدك

class JoinProviderPage extends StatelessWidget {
  final controller = Get.put(ProviderCategoriesController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(title: Text('Select Service Category')),
        body: ObsListBuilder(
          obs: controller.categories,
          builder: (context, categories) {
            if (categories.isEmpty) {
              return Center(child: Text("No categories found."));
            }
            return ListView.separated(
              itemCount: categories.length,
              separatorBuilder: (_, __) => Divider(),
              itemBuilder: (context, index) {
                final category = categories[index];
                print('rtf ${category.svg}');

                return InkWell(
                    // onTap: () =>   Get.toNamed(Pages.register.value) ,
                    child: servicecard(category));
              },
            );
          },
        ));
  }

  ListTile servicecard(ProviderCategory category) {
    return ListTile(
      leading:
          // SvgPicture.string(
          //   category.svg, // هنا نص SVG مباشرة
          //   height: 40,
          //   width: 40,
          // ),

          // placeholderBuilder: (context) =>
          // CircularProgressIndicator(),
          // ),
          Image.network(
        category.svg,
        // width: 60,
        // height: 60,
        fit: BoxFit.cover,
        errorBuilder: (c, e, s) => Icon(Icons.broken_image),
      ),
      title: Text(
        category.title,
      ),
      subtitle: Text(
        // "df",
        'Providers: ${category.providerCount}\nKeywords: ${category.keywords.join(", ")}',
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
      onTap: () {
        // تابع حسب حاجتك
      },
    );
  }
}
